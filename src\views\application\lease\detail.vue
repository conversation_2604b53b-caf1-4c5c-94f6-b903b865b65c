<template>
    <div>
        <!-- Header Start -->
        <el-card class="!border-none" shadow="never">
            <el-page-header :content="pageTitle" @back="$router.back()" />
        </el-card>
        <!-- Header End -->

        <div class="lease-detail-main">
            <!-- 申请信息 Start -->
            <el-card class="!border-none mt-4" shadow="never" style="padding: 0 20px">
                <template #header>
                    <div class="card-header">
                        <span class="font-medium nr">申请信息</span>
                    </div>
                </template>
                <!-- 申请信息 -->
                <el-form :inline="true" :model="detailData" label-width="auto" v-loading="loading">
                    <div class="flex justify-around">
                        <div class="w-3/12">
                            <!-- <el-form-item label="申请ID: ">
                                <div class="content">{{ detailData.id || '-' }}</div>
                            </el-form-item> -->
                            <el-form-item label="联系人: ">
                                <div class="content">{{ detailData.name || '-' }}</div>
                            </el-form-item>
                            <el-form-item label="申请时间: ">
                                <div class="content">{{ detailData.create_time || '-' }}</div>
                            </el-form-item>
                            <el-form-item label="更新时间: ">
                                <div class="content">{{ detailData.update_time || '-' }}</div>
                            </el-form-item>
                        </div>
                        <div class="w-3/12">
                            <el-form-item label="手机号码: ">
                                <div class="content">{{ detailData.mobile || '-' }}</div>
                            </el-form-item>
                            <el-form-item label="租用时长: ">
                                <div class="content">{{ detailData.duration }}天</div>
                            </el-form-item>
                        </div>
                        <div class="w-3/12">
                            <el-form-item label="状态: ">
                                <div class="content">
                                    <el-tag :type="getStatusType(detailData.status)">
                                        {{ detailData.status_str }}
                                    </el-tag>
                                </div>
                            </el-form-item>
                            <!-- <el-form-item label="备注: ">
                                <div class="content">{{ detailData.remark || '-' }}</div>
                            </el-form-item> -->
                        </div>
                    </div>
                </el-form>
                <!-- Button Group Start -->
                <div class="button-group" v-if="detailData.status == 0">
                    <div class="mt-6 flex gap-3">
                        <el-button
                            v-perms="['drone.lease/enable']"
                            type="success"
                            @click="handleStatus(1)"
                        >
                            出租
                        </el-button>
                        <el-button
                            v-perms="['drone.lease/close']"
                            type="danger"
                            @click="handleStatus(2)"
                        >
                            关闭
                        </el-button>
                    </div>
                </div>
                <!-- Button Group End -->
            </el-card>
            <!-- 申请信息 End -->

            <!-- 租用无人机列表 Start -->
            <el-card class="!border-none mt-4" shadow="never" style="padding: 0 20px">
                <template #header>
                    <span class="font-medium nr">租用无人机列表</span>
                </template>
                <!-- 无人机列表 -->
                <el-table
                    ref="tableDataRef"
                    :data="detailData.drone || []"
                    table-layout="auto"
                    style="width: 100%"
                >
                    <el-table-column label="无人机名称" min-width="200">
                        <template #default="scope">
                            <div class="flex items-center">
                                <!-- <el-image
                                    style="width: 58px; height: 58px"
                                    :src="scope.row.image"
                                    :fit="'cover'"
                                ></el-image> -->
                                <div class="justify-center ml-2">{{ scope.row.platform_drone.title }}</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column property="deposit" label="押金" min-width="100">
                        <template #default="scope">
                            ¥{{ scope.row.deposit }}
                        </template>
                    </el-table-column>
                    <el-table-column property="unit_price" label="单价" min-width="100">
                        <template #default="scope">
                            ¥{{ scope.row.price }}/天
                        </template>
                    </el-table-column>
                    <el-table-column property="total_price" label="总价" min-width="100">
                        <template #default="scope">
                            ¥{{ scope.row.total_price }}
                        </template>
                    </el-table-column>
                </el-table>
                <div class="flex justify-end mt-[20px] items-end mr-[40px]">
                    <span>合计：</span>
                    <span class="text-[#F56C6C] font-bold text-[18px]">
                        ¥{{ detailData.total_amount || '0.00' }}
                    </span>
                </div>
            </el-card>
            <!-- 租用无人机列表 End -->
        </div>
    </div>
</template>

<script lang="ts" setup name="leaseDetail">
import { apiLeaseDetail, apiLeaseStatus } from '@/api/marketing/lease'
import feedback from '@/utils/feedback'

const route = useRoute()

const pageTitle = '租赁申请详情'
const loading = ref(false)

const detailData = reactive({
    id: '',
    name: '',
    mobile: '',
    duration: '',
    status: 0,
    status_str: '',
    create_time: '',
    update_time: '',
    remark: '',
    drone: [] as any[],
    total_amount: ''
})

// 获取状态类型（与无人机列表页面保持一致）
const getStatusType = (status: number) => {
    switch (status) {
        case 0: return ''         // 待联系 - 默认样式
        case 1: return 'success'  // 已出租 - 绿色
        case 2: return 'danger'   // 已关闭 - 红色
        default: return ''
    }
}

// 获取详情
const getDetail = async () => {
    loading.value = true
    try {
        const data = await apiLeaseDetail({
            id: route.query.id
        })
        Object.keys(detailData).forEach((key) => {
            //@ts-ignore
            detailData[key] = data[key]
        })
    } finally {
        loading.value = false
    }
}

// 状态操作（出租/关闭）
const handleStatus = async (status: number) => {
    const actionText = status === 1 ? '出租' : '关闭'
    await feedback.confirm(`确定要${actionText}这个租赁申请吗？`)
    await apiLeaseStatus({ id: detailData.id, status })
    // 重新获取详情以更新状态
    getDetail()
}

onMounted(() => {
    if (route.query.id) {
        getDetail()
    }
})
</script>

<style lang="scss">
.lease-detail-main .el-card__header,
.lease-detail-main .el-card__body {
    padding: calc(var(--el-card-padding) - 2px) 0;
}
.content {
    width: 24vw;
}
.button-group {
    border-top: 1px solid #f2f2f2;
    padding-top: 15px;
}
</style>
