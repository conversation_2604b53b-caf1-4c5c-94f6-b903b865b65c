<template>
    <el-card class="!border-none" shadow="never">
        <el-page-header content="团队详情" @back="$router.back()" />
    </el-card>

    <el-card shadow="never" class="mt-4 !border-none">
        <template #header>
            <span class="text-lg font-semibold">基本资料</span>
        </template>
        <el-form label-position="right" label-width="auto">
            <el-form-item label="团队ID:">
                {{ detail.id }}
            </el-form-item>
            <el-form-item label="名称:">
                {{ detail.name }}
            </el-form-item>
            <el-form-item label="编号:">
                {{ detail.code }}
            </el-form-item>
            <el-form-item label="创建人:">
                {{ detail.staff.name }}
            </el-form-item>
            <el-form-item label="成员数:">
                {{ detail.member_count }}
            </el-form-item>
            <el-form-item label="飞手订单数:">
                {{ detail.order_count }}
            </el-form-item>
            <el-form-item label="佣金总额:">
                {{ detail.commission }}
            </el-form-item>
            <el-form-item label="创建时间:">
                {{ detail.create_time }}
            </el-form-item>
        </el-form>
    </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getTeamDetail } from '@/api/master_worker/team'

const route = useRoute()
const detail = ref<any>({ staff: {} })

const getDetail = async () => {
    const res = await getTeamDetail({ id: route.query.id })
    detail.value = res
}

onMounted(() => {
    getDetail()
})
</script>
