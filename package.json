{"name": "vue-project", "version": "0.0.0", "license": "MIT", "scripts": {"dev": "vite", "preview": "vite preview --port 4173", "build": "vite build ", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.0.6", "@highlightjs/vue-plugin": "^2.1.0", "@wangeditor/editor": "^5.1.12", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^0.27.2", "css-color-function": "^1.3.3", "echarts": "^5.3.3", "element-plus": "^2.8.6", "highlight.js": "^11.6.0", "nprogress": "^0.2.0", "pinia": "^2.0.14", "vue": "^3.2.37", "vue-clipboard3": "^2.0.0", "vue-echarts": "^6.2.3", "vue-router": "^4.0.16", "vue3-video-play": "^1.3.1-beta.6", "vuedraggable": "^4.1.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.1.0", "@types/lodash-es": "^4.17.6", "@types/node": "^16.11.41", "@types/nprogress": "^0.2.0", "@vitejs/plugin-vue": "^3.0.0", "@vitejs/plugin-vue-jsx": "^2.0.0", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.0", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.7", "consola": "^2.15.3", "eslint": "^8.5.0", "eslint-plugin-vue": "^9.0.0", "postcss": "^8.4.14", "prettier": "^2.5.1", "sass": "^1.53.0", "tailwindcss": "^3.0.24", "typescript": "~4.7.4", "unplugin-auto-import": "^0.9.2", "unplugin-vue-components": "^0.19.9", "vite": "^3.0.0", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-tsc": "^0.38.1"}}