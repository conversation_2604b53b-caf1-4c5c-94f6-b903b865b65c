/**
变量
 */

/* Color
-------------------------- */
$color-primary: #4A5DFF !default;
$color-success: #67c23a !default;
$color-warning: #fb9400 !default;
$color-danger: #f56c6c !default;
$color-error: #db2828 !default;
$color-info: #909399 !default;
$color-white: #ffffff !default;
$color-black: #333333 !default;

$color-primary-light-1: mix($color-white, $color-primary, 10%) !default;
$color-primary-light-2: mix($color-white, $color-primary, 20%) !default;
$color-primary-light-3: mix($color-white, $color-primary, 30%) !default;
$color-primary-light-4: mix($color-white, $color-primary, 40%) !default;
$color-primary-light-5: mix($color-white, $color-primary, 50%) !default;
$color-primary-light-6: mix($color-white, $color-primary, 60%) !default;
$color-primary-light-7: mix($color-white, $color-primary, 70%) !default;
$color-primary-light-8: mix($color-white, $color-primary, 80%) !default;
$color-primary-light-9: mix($color-white, $color-primary, 90%) !default;


/* Font
-------------------------- */

$color-text-primary: #333333 !default;
$color-text-regular: #666666 !default;
$color-text-secondary: #999999 !default;
$color-text-placeholder: #999999 !default;
$font-size-xl: 17px !default;
$font-size-lg: 16px !default;
$font-size-md: 15px !default;
$font-size-nr: 14px !default;
$font-size-sm: 13px !default;
$font-size-xs: 12px !default;
$font-size-base: $font-size-sm;

/* Background
-------------------------- */

$background-color-base: #f6f6f6 !default;

/* Border
-------------------------- */

$border-color-base: #e5e5e5 !default;
$border-color-light: #f2f2f2 !default;
$border-width-base: 1px !default;
$border-style-base: solid !default;
$border-color-hover: $color-primary !default;
$border-base: $border-width-base $border-style-base $border-color-base !default;

/* Layout
-------------------------- */
$layout-min-width: 1200px!default;;
$layout-aside-width: 200px !default;
$layout-header-height: 70px !default;



$colors: (
	"primary": (
		"base": $color-primary,
	),
	"success": (
		"base": $color-success,
	),
	"warning": (
		"base": $color-warning,
	),
	"danger": (
		"base": $color-danger,
	),
	"error": (
		"base": $color-error,
	),
	"info": (
		"base": $color-info,
	),
);

$text-color: (
    'primary': $color-text-primary,
    'regular': $color-text-regular,
    'secondary': $color-text-secondary,
    'placeholder': $color-text-placeholder,
);

$border-color: (
    'base': $border-color-base,
    'light': $border-color-light,
    'lighter': $border-color-light,
    'extra-light': $border-color-light,
);


$font-size:(
    'extra-large': $font-size-xl,
    'large': $font-size-lg,
    'medium': $font-size-md,
    'base': $font-size-base,
    'small': $font-size-sm,
    'extra-small': $font-size-xs,
);

$button-font-size:(
    'default': $font-size-base,
    'medium': $font-size-base,
    'small': $font-size-base,
    'mini': $font-size-sm,
);


$button-padding-vertical:(
    'default': 12px,
    'medium': 10px,
    'small': 8px,
    'mini': 6px,
);

$button-padding-horizontal:(
    'default': 25px,
    'medium': 25px,
    'small': 20px,
    'mini': 15px,
);

$table:(
    'text-color': $color-text-primary,
    'header-text-color': $color-text-primary,
    'header-bg-color': rgba($color-primary, 0.05),
);

// 替换elementui的变量
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
	$colors: $colors,
	$text-color: $text-color,
	$border-color: $border-color,
	$font-size: $font-size,
	$button-font-size: $button-font-size,
	$button-padding-vertical: $button-padding-vertical,
	$table: $table
);


