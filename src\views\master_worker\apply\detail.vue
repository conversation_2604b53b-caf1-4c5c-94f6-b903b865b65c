<template>
    <el-card shadow="never" class="!border-none">
        <el-page-header content="审核" @back="$router.back()" />
    </el-card>

    <!-- 审核拒绝状态显示 -->
    <el-card shadow="never" style="margin-top: 15px" class="!border-none" v-if="formData.apply_status == 2">
        <div class="pl-[80px] pr-[100px]">
            <span class="font-bold text-[18px]">审核拒绝</span>
            <span class="text-[#f63737] ml-[30px]">原因：{{ formData.remarks }}</span>
        </div>
    </el-card>

    <!-- 基本信息 -->
    <el-card shadow="never" style="margin-top: 15px" class="!border-none">
        <div class="font-bold text-[20px] mb-[30px]">基本信息</div>
        <div class="grid grid-cols-2 gap-4">
            <div class="flex items-center">
                <span class="text-gray-600 w-24">ID:</span>
                <span>{{ formData.id }}</span>
            </div>
            <div class="flex items-center">
                <span class="text-gray-600 w-24">师傅ID:</span>
                <span>{{ formData.staff_id }}</span>
            </div>
            <div class="flex items-center">
                <span class="text-gray-600 w-24">师傅姓名:</span>
                <span>{{ formData.name }}</span>
            </div>
            <div class="flex items-center">
                <span class="text-gray-600 w-24">手机号:</span>
                <span>{{ formData.mobile }}</span>
            </div>
            <div class="flex items-center">
                <span class="text-gray-600 w-24">性别:</span>
                <span>{{ formData.sex == 1 ? '男' : formData.sex == 2 ? '女' : '未知' }}</span>
            </div>
            <div class="flex items-center">
                <span class="text-gray-600 w-24">申请类型:</span>
                <span>{{ formData.apply_type == 1 ? '个人' : formData.apply_type == 2 ? '公司' : '' }}</span>
            </div>
            <div class="flex items-center">
                <span class="text-gray-600 w-24">审核状态:</span>
                <span :class="{'text-orange-500': formData.apply_status == 0, 'text-green-500': formData.apply_status == 1, 'text-red-500': formData.apply_status == 2}">
                    {{ formData.apply_status == 0 ? '待审核' : formData.apply_status == 1 ? '审核通过' : formData.apply_status == 2 ? '审核失败' : '' }}
                </span>
            </div>
            <div class="flex items-center">
                <span class="text-gray-600 w-24">地址:</span>
                <span>{{ formData.province }}{{ formData.city }}{{ formData.district }}</span>
            </div>
            <div class="flex items-center">
                <span class="text-gray-600 w-24">申请时间:</span>
                <span>{{ formData.create_time }}</span>
            </div>
        </div>
        <div class="mt-4" v-if="formData.remarks && formData.apply_status > 1">
            <div class="flex items-start">
                <span class="text-gray-600 w-24">审核备注:</span>
                <span>{{ formData.remarks }}</span>
            </div>
        </div>
    </el-card>

    <!-- 无人机信息 -->
    <el-card shadow="never" style="margin-top: 15px" class="!border-none" v-if="formData.drone_type_id">
        <div class="font-bold text-[20px] mb-[30px]">无人机信息</div>
        <div class="flex items-center">
            <span class="text-gray-600 w-24">无人机型:</span>
            <span>{{ formData.drone_type_id == 1 ? '小型' : formData.drone_type_id == 2 ? '中型' : formData.drone_type_id == 3 ? '大型' : '' }}</span>
        </div>
        <div class="mt-4" v-if="formData.drone_img && formData.drone_img.length > 0">
            <div class="flex items-start">
                <span class="text-gray-600 w-24">无人机照片:</span>
                <div class="flex flex-wrap gap-4">
                    <div v-for="(img, index) in getDroneImages(formData.drone_img)" :key="index" class="mr-[20px]">
                        <el-image :src="img" fit="cover" style="width: 100px;height: 100px;border-radius: 6px;" :preview-src-list="getDroneImages(formData.drone_img)" />
                    </div>
                </div>
            </div>
        </div>
    </el-card>

    <!-- 证件照片 -->
    <el-card shadow="never" style="margin-top: 15px" class="!border-none">
        <div class="font-bold text-[20px] mb-[30px]">证件照片</div>
        <div class="flex flex-wrap gap-8">
            <div v-if="formData.identity_portrait_image" class="text-center">
                <el-image :src="formData.identity_portrait_image" fit="cover" style="width: 120px;height: 120px;border-radius: 6px;" :preview-src-list="[formData.identity_portrait_image]" />
                <div class="text-center mt-[5px] text-sm text-gray-600">身份证人像面</div>
            </div>
            <div v-if="formData.identity_emblem_image" class="text-center">
                <el-image :src="formData.identity_emblem_image" fit="cover" style="width: 120px;height: 120px;border-radius: 6px;" :preview-src-list="[formData.identity_emblem_image]" />
                <div class="text-center mt-[5px] text-sm text-gray-600">身份证国徽面</div>
            </div>
            <div v-if="formData.work_image" class="text-center">
                <el-image :src="formData.work_image" fit="cover" style="width: 120px;height: 120px;border-radius: 6px;" :preview-src-list="[formData.work_image]" />
                <div class="text-center mt-[5px] text-sm text-gray-600">头像</div>
            </div>
        </div>
    </el-card>

    <!-- 资质证件 -->
    <el-card shadow="never" style="margin-top: 15px" class="!border-none" v-if="(formData.business_img || formData.drive_img || (formData.contract_img && formData.contract_img.length > 0))">
        <div class="font-bold text-[20px] mb-[30px]">资质证件</div>
        <div class="flex flex-wrap gap-8">
            <div v-if="formData.business_img" class="text-center">
                <el-image :src="formData.business_img" fit="cover" style="width: 120px;height: 120px;border-radius: 6px;" :preview-src-list="[formData.business_img]" />
                <div class="text-center mt-[5px] text-sm text-gray-600">营业执照</div>
            </div>
            <div v-if="formData.drive_img" class="text-center">
                <el-image :src="formData.drive_img" fit="cover" style="width: 120px;height: 120px;border-radius: 6px;" :preview-src-list="[formData.drive_img]" />
                <div class="text-center mt-[5px] text-sm text-gray-600">驾驶证</div>
            </div>
        </div>
        <div class="mt-4" v-if="formData.contract_img && formData.contract_img.length > 0">
            <div class="flex items-start">
                <span class="text-gray-600 w-24">合同图片:</span>
                <div class="flex flex-wrap gap-4">
                    <div v-for="(img, index) in getContractImages(formData.contract_img)" :key="index" class="text-center">
                        <el-image :src="img" fit="cover" style="width: 100px;height: 100px;border-radius: 6px;" :preview-src-list="getContractImages(formData.contract_img)" />
                        <div class="text-center mt-[5px] text-sm text-gray-600">合同{{ index + 1 }}</div>
                    </div>
                </div>
            </div>
        </div>
    </el-card>
    <footer-btns v-if="formData.apply_status == 0">
        <el-button type="danger" @click="verify(2)">拒绝</el-button>
        <el-button type="primary" @click="verify(1)">通过</el-button>
    </footer-btns>

    <!-- 审核拒绝弹窗 -->
    <Verify
        v-if="verifyRefShow"
        ref="verifyRef"
        :id="id"
        :apply_status="2"
        @close="verifyRefShow = false"
        @confirm="router.back()"
    ></Verify>
</template>

<script lang="ts" setup>
import { apiMasterWorkerApplyDetail, apiMasterWorkerApplyApply } from '@/api/master_worker/apply'
import { ref, shallowRef, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import FooterBtns from '@/components/footer-btns/index.vue'
import Verify from './components/verify.vue'

/** Data Start **/
const route = useRoute()
const router = useRouter()
const id: any = route.query.id
const formData = ref(<any>{})
const verifyRef = shallowRef()
const verifyRefShow = ref(false)
/** Data End **/

/** Methods Start **/
// 获取详情
const getMasterWorkerDetail = async (id: number): Promise<void> => {
    ;(formData.value as {}) = await apiMasterWorkerApplyDetail({ id })
}

// 审核
const verify = async (applyStatus: number): Promise<void> => {
    if (applyStatus == 2) {
        verifyRefShow.value = true
        verifyRef.value?.open()
    }
    if (applyStatus == 1) {
        await apiMasterWorkerApplyApply({ 'id': id, 'apply_status': applyStatus })
        router.back()
    }
}

// 格式化时间
const formatTime = (timestamp: number): string => {
    if (!timestamp) return ''
    const date = new Date(timestamp * 1000)
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    })
}

// 处理无人机图片数组
const getDroneImages = (droneImg: any): string[] => {
    if (!droneImg) return []
    if (typeof droneImg === 'string') {
        try {
            return JSON.parse(droneImg)
        } catch {
            return [droneImg]
        }
    }
    if (Array.isArray(droneImg)) {
        return droneImg
    }
    return []
}

// 处理合同图片数组
const getContractImages = (contractImg: any): string[] => {
    if (!contractImg) return []
    if (typeof contractImg === 'string') {
        try {
            return JSON.parse(contractImg)
        } catch {
            return [contractImg]
        }
    }
    if (Array.isArray(contractImg)) {
        return contractImg
    }
    return []
}
/** Methods End **/

/** LifeCycle Start **/
onMounted(async () => {
    // 请求详情
    if (id) {
        await getMasterWorkerDetail(id)
    }
})
/** LifeCycle End **/
</script>

<style lang="scss" scoped>
.ls-input,
.select {
    width: 340px;
}
</style>
