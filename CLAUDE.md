# CLAUDE.md

该文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 项目概述

这是一个使用 Vite 构建的 Vue 3 和 TypeScript 项目。它使用 Pinia 进行状态管理，使用 Vue Router 进行路由，并使用 Element Plus作为组件库。

路由是动态处理的。路由在运行时根据从后端获取的数据生成。其核心逻辑位于 `src/router/index.ts` 中，特别是在 `filterAsyncRoutes` 和 `loadRouteView` 函数中。静态路由在 `src/router/routes.ts` 中定义。

- **`src/`**: 包含主要源代码。
- **`src/api/`**: 管理所有到后端的 API 请求。
- **`src/assets/`**: 存储图片和图标等静态资源。
- **`src/components/`**: 存放整个应用中可复用的 Vue 组件。
- **`src/layout/`**: 定义了主应用的布局结构。
- **`src/router/`**: 配置应用的路由。如前所述，大多数路由是动态生成的。
- **`src/stores/`**: 包含用于全局状态管理的 Pinia store。
- **`src/views/`**: 包含应用的不同页面，由路由动态加载。

## 页面样式

- 接口配置参考 src/api/app.ts
- 列表页请参考 src/views/master_worker/lists/index.vue
- 详情页请参考 src/views/master_worker/apply/detail.vue
- 编辑页请参考 src/views/master_worker/lists/edit.vue

## 其它

- 使用中文回答、处理所有内容；
- 执行任务前，必须先构建任务计划，像我确认，我同意后才可以执行
- 新增内容时，如果板式不确定，请参考其它文件内容