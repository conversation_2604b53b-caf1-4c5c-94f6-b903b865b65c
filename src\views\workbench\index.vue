<template>
    <div class="workbench">
        <div class="lg:flex">
            <!-- <el-card
                class="!border-none mb-4 md:mr-4"
                shadow="never"
                :body-style="{
                    width: '400px'
                }"
            >
                <template #header>
                    <span class="card-title">平台信息</span>
                </template>
                <div>
                    <div class="flex leading-9">
                        <div class="mr-3">平台名称</div>
                        <div>{{ workbenchData.version.website }}</div>
                    </div>
                    <div class="flex leading-9">
                        <div class="mr-3">当前版本</div>
                        <div class="flex">
                            <div>{{ workbenchData.version.version }}</div>

                            <div class="ml-5 text-xs text-[#F36161]" v-if="checkData.hasNewVersion">
                                有新的版本可用
                            </div>
                        </div>
                    </div>
                    <div class="flex leading-9 items-center" v-if="config.document_status">
                        <is-copyright />

                        <div>
                            <el-tag type="success" class="mr-2 ml-2" effect="dark">
                                <a href="https://www.likeshop.cn/" target="_blank">官网 </a>
                            </el-tag>
                            <el-tag type="danger" class="mx-1" effect="dark">
                                <a href="https://www.likeshop.cn/doc" target="_blank"> 开发文档 </a>
                            </el-tag>
                        </div>
                    </div>
                </div>
            </el-card> -->

            <el-card class="!border-none mb-4 flex-1" shadow="never">
                <template #header>
                    <div>
                        <span class="card-title">数据统计</span>
                        <span class="text-tx-secondary text-xs ml-4">
                            更新时间：{{ workbenchData.today.time }}
                        </span>
                    </div>
                </template>

                <div class="flex flex-wrap">
                    <div class="w-1/2 md:w-1/4">
                        <div class="leading-10">订单总销量（笔）</div>
                        <div class="text-6xl">
                            {{ workbenchData.today.today_sales_count }}
                        </div>
                        <div class="text-tx-secondary text-xs">
                            总销量：{{ workbenchData.today.total_sales_amount }}
                        </div>
                    </div>
                    <div class="w-1/2 md:w-1/4">
                        <div class="leading-10">今日新增用户（人）</div>
                        <div class="text-6xl">{{ workbenchData.today.today_new_user }}</div>
                        <div class="text-tx-secondary text-xs">
                            总用户量：{{ workbenchData.today.total_new_user }}
                        </div>
                    </div>
                    <div class="w-1/2 md:w-1/4">
                        <div class="leading-10">今日新增飞手（人）</div>
                        <div class="text-6xl">{{ workbenchData.today.today_new_staff }}</div>
                        <div class="text-tx-secondary text-xs">
                            总飞手量：{{ workbenchData.today.total_new_staff }}
                        </div>
                    </div>
                    <div class="w-1/2 md:w-1/4">
                        <div class="leading-10">今日新增团队数</div>
                        <div class="text-6xl">{{ workbenchData.today.today_new_team }}</div>
                        <div class="text-tx-secondary text-xs">
                            总团队数：{{ workbenchData.today.total_new_team }}
                        </div>
                    </div>
                </div>
            </el-card>
        </div>
        <!-- <div class="function mb-4">
            <el-card class="flex-1 !border-none" shadow="never">
                <template #header>
                    <span>常用功能</span>
                </template>
                <div class="flex flex-wrap">
                    <div
                        v-for="item in workbenchData.menu"
                        class="md:w-[12.5%] w-1/4 flex flex-col items-center"
                        :key="item"
                    >
                        <router-link :to="item.url" class="mb-3 flex flex-col items-center">
                            <image-contain width="40px" height="40px" :src="item?.image" />
                            <div class="mt-2">{{ item.name }}</div>
                        </router-link>
                    </div>
                </div>
            </el-card>
        </div> -->
        <div class="lg:flex">
            <el-card class="flex-1 !border-none mb-4" shadow="never">
                <template #header>
                    <span>销售额趋势图</span>
                </template>
                <div>
                    <div>
                        <el-form>
                            <data-picker
                                class="ls-input mr-4"
                                style="width: 280px"
                                v-model:start_time="formData.start_time"
                                v-model:end_time="formData.end_time"
                            ></data-picker>
                            <el-button type="primary" @click="getBusinessData">查询</el-button>
                        </el-form>          
                    </div>
                    <v-charts
                        style="height: 350px"
                        :option="workbenchData.businessOption"
                        :autoresize="true"
                    />
                </div>
            </el-card>

            <!-- <el-card class="flex-1 !border-none mb-4" shadow="never">
                <template #header>
                    <span>用户访问量（近15天）</span>
                </template>
                <div>
                    <v-charts
                        style="height: 350px"
                        :option="workbenchData.visitorOption"
                        :autoresize="true"
                    />
                </div>
            </el-card> -->
        </div>

        <div class="text-sm text-[#999] mt-8">
            <div class="text-center mt-[10px]" v-if="config.copyright_config">
                <a :href="config.copyright_config[0]?.value" target="_blank">{{
                    config.copyright_config[0]?.key
                }}</a>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup name="workbench">
import { getWorkbench, apiCheckVersion, apiGetBusiness } from '@/api/app'
import vCharts from 'vue-echarts'
import useAppStore from '@/stores/modules/app'
import feedback from '@/utils/feedback'

// 表单数据
const appStore = useAppStore()
const config = computed(() => appStore.config)
const formData: any = reactive({
    start_time: "",
    end_time: ""
})
const workbenchData: any = reactive({
    version: {
        version: '', // 系统版本
        website: '' // 平台名称
    },
    today: {}, // 今日数据
    menu: [], // 常用功能
    visitor: [], // 访问量
    business: [], // 销售额

    businessOption: {
        xAxis: {
            type: 'category',
            data: []
        },
        yAxis: {
            type: 'value'
        },
        itemStyle: {
            // 点的颜色。
            color: 'red'
        },
        tooltip: {
            trigger: 'axis'
        },
        series: [
            {
                name: '销售量',
                data: [],
                type: 'line'
            }
        ]
    },

    visitorOption: {
        xAxis: {
            type: 'category',
            data: []
        },
        yAxis: {
            type: 'value'
        },
        itemStyle: {
            // 点的颜色。
            color: 'red'
        },
        tooltip: {
            trigger: 'axis'
        },
        series: [
            {
                name: '访问量',
                data: [],
                type: 'line'
            }
        ]
    }
})

// 获取销售额数据
const getBusinessData = async () => {
    /* if (formData.start_time && formData.end_time) {
        const startTime = new Date(formData.start_time).getTime()
        const endTime = new Date(formData.end_time).getTime()
        const diff = (endTime - startTime) / (1000 * 60 * 60 * 24)
        if (diff + 1 > 30) {
            return feedback.msgError('最多允许查询30天的数据')
        }
    } */
    const res = await apiGetBusiness(formData)
    workbenchData.businessOption.xAxis.data = res.date.reverse()
    workbenchData.businessOption.series[0].data = res.list[0].data.reverse()
}

// 获取工作台主页数据
const getData = async () => {
    const res = await getWorkbench()
    workbenchData.version = res.version
    workbenchData.today = res.today
    workbenchData.menu = res.menu
    workbenchData.visitor = res.visitor
    workbenchData.support = res.support

    // 清空echarts 数据
    workbenchData.visitorOption.xAxis.data = []
    workbenchData.visitorOption.series[0].data = []

    // 写入从后台拿来的数据
    workbenchData.visitorOption.xAxis.data = res.visitor.date.reverse()
    workbenchData.visitorOption.series[0].data = res.visitor.list[0].data.reverse()
}
// 检测新版本
const checkData = reactive({
    hasNewVersion: false,
    newVersion: ''
})
const getCheckVersion = async () => {
    const res = await apiCheckVersion(null)
    checkData.hasNewVersion = res.result
    checkData.newVersion = res.version
}
onMounted(() => {
    getData()
    getBusinessData()
    getCheckVersion()
})
</script>

<style lang="scss" scoped></style>
