<template>
    <el-card shadow="never" class="!border-none">
        <el-form :inline="true" :model="query" class="demo-form-inline">
            <el-form-item label="团队ID">
                <el-input v-model="query.id" placeholder="请输入团队ID" clearable class="ls-input" />
            </el-form-item>
            <el-form-item label="名称/编号">
                <el-input v-model="query.keyword" placeholder="请输入名称/编号" clearable class="ls-input" />
            </el-form-item>
            <el-form-item label="创建人">
                <el-input v-model="query.staff_name" placeholder="请输入创建人" clearable class="ls-input" />
            </el-form-item>
            <el-form-item label="创建时间">
                <data-picker
                    class="ls-input"
                    v-model:start_time="query.start_time"
                    v-model:end_time="query.end_time"
                ></data-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleQuery">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
            </el-form-item>
        </el-form>
    </el-card>

    <el-card shadow="never" class="mt-4 !border-none">
        <el-table :data="list" v-loading="loading">
            <el-table-column prop="id" label="团队ID" />
            <el-table-column prop="name" label="名称" />
            <el-table-column prop="code" label="编号" />
            <el-table-column prop="staff_name" label="创建人" />
            <el-table-column prop="member_count" label="成员数量" />
            <el-table-column prop="order_count" label="订单总数量" />
            <el-table-column prop="commission" label="佣金总额" />
            <el-table-column prop="create_time" label="创建时间" />
            <el-table-column label="操作">
                <template #default="{ row }">
                    <router-link
                        v-perms="['staff.team/detail']"
                        :to="{
                            path: '/master_worker/team/detail',
                            query: {
                                id: row.id
                            }
                        }"
                    >
                        <el-link type="primary" :underline="false">详情</el-link>
                    </router-link>
                    <router-link
                        v-perms="['staff.team/member']"
                        class="ml-2"
                        :to="{
                            path: '/master_worker/lists',
                            query: {
                                team_id: row.id
                            }
                        }"
                    >
                        <el-link type="primary" :underline="false">成员</el-link>
                    </router-link>
                </template>
            </el-table-column>
        </el-table>

        <div class="flex justify-end mt-4">
             <pagination v-model="pager" @change="getList" />
        </div>
    </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { getTeamList } from '@/api/master_worker/team'
import DataPicker from '@/components/data-picker/index.vue'
import Pagination from '@/components/pagination/index.vue'
import { usePaging } from '@/hooks/usePaging'

const query = reactive({
    id: '',
    keyword: '',
    staff_name: '',
    start_time: '',
    end_time: ''
})

const { pager, getLists, resetPage, resetParams } = usePaging({
    fetchFun: getTeamList,
    params: query
})

const list = ref([])
const loading = ref(false)

const getList = async () => {
    loading.value = true
    try {
        const res = await getLists()
        list.value = res.lists
    } finally {
        loading.value = false
    }
}

onMounted(() => {
    getList()
})

const handleQuery = () => {
    resetPage()
    getList()
}

const handleReset = () => {
    resetParams()
    getList()
}
</script>

<style lang="scss">
.ls-input {
    width: 280px;
}
</style>

