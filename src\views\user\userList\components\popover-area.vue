<template>
  <div class="popover-area">
    <el-popover placement="top" v-model:visible="visible" :width="width" trigger="manual" :hide-after="0"
      :teleported="true" :persistent="true" :show-arrow="false" :popper-options="{
        modifiers: [{ name: 'eventListeners', options: { scroll: false, resize: true } }]
      }">
      <div class="flex" @click.stop>
        <div class="popover-input__input m-r-10 flex-1 flex">
          <el-select v-model="selectedProvince" placeholder="请选择省" size="mini" class="flex-1 mr-2"
            @change="handleProvinceChange" popper-class="select-dropdown-in-popover" :teleported="false">
            <el-option v-for="item in provinceList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select v-model="selectedCity" placeholder="请选择市" size="mini" class="flex-1 mr-2" @change="handleCityChange"
            :disabled="!selectedProvince" popper-class="select-dropdown-in-popover" :teleported="false">
            <el-option v-for="item in cityList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select v-model="selectedArea" placeholder="请选择区/县" size="mini" class="flex-1 mr-2"
            :disabled="!selectedCity" popper-class="select-dropdown-in-popover" :teleported="false">
            <el-option v-for="item in areaList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="popover-input__btns flex-none">
          <el-button type="text" @click="close">取消</el-button>
          <el-button type="primary" @click="handleConfirm" :disabled="!isValid">确定</el-button>
        </div>
      </div>
      <template #reference>
        <div class="inline" type="text" @click="open">
          <slot></slot>
        </div>
      </template>
    </el-popover>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from 'vue'
import areaData from '@/utils/area'

// 定义区域数据类型
interface AreaItem {
  value: number;
  pid: number;
  label: string;
  children?: AreaItem[];
}

export default defineComponent({
  props: {
    modelValue: {
      type: Object,
      default: () => ({
        province: '',
        province_id: '',
        city: '',
        city_id: '',
        area: '',
        area_id: ''
      })
    },
    width: {
      type: Number,
      default: 550,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['confirm', 'update:modelValue'],
  setup(props, { emit }) {
    const visible = ref(false)
    const selectedProvince = ref<number | string>('')
    const selectedCity = ref<number | string>('')
    const selectedArea = ref<number | string>('')

    // 省市区数据列表
    const provinceList = ref<AreaItem[]>(areaData as AreaItem[])
    const cityList = ref<AreaItem[]>([])
    const areaList = ref<AreaItem[]>([])

    // 省市区名称
    const provinceName = ref('')
    const cityName = ref('')
    const areaName = ref('')

    // 表单是否有效
    const isValid = computed(() => {
      return selectedProvince.value && selectedCity.value && selectedArea.value
    })

    // 处理省份变化
    const handleProvinceChange = (value: number | string) => {
      selectedCity.value = ''
      selectedArea.value = ''
      cityName.value = ''
      areaName.value = ''

      const province = provinceList.value.find(item => item.value === value)
      if (province) {
        provinceName.value = province.label
        cityList.value = province.children || []
      } else {
        cityList.value = []
      }
      areaList.value = []
    }

    // 处理城市变化
    const handleCityChange = (value: number | string) => {
      selectedArea.value = ''
      areaName.value = ''

      const city = cityList.value.find(item => item.value === value)
      if (city) {
        cityName.value = city.label
        areaList.value = city.children || []
      } else {
        areaList.value = []
      }
    }

    // 初始化数据
    const initData = () => {
      if (props.modelValue) {
        // 设置省份
        if (props.modelValue.province_id) {
          selectedProvince.value = props.modelValue.province_id
          handleProvinceChange(selectedProvince.value)

          // 设置城市
          if (props.modelValue.city_id) {
            selectedCity.value = props.modelValue.city_id
            handleCityChange(selectedCity.value)

            // 设置区县
            if (props.modelValue.area_id) {
              selectedArea.value = props.modelValue.area_id

              // 设置区县名称
              const area = areaList.value.find(item => item.value === selectedArea.value)
              if (area) {
                areaName.value = area.label
              }
            }
          }
        }
      }
    }

    // 监听区县变化，更新区县名称
    watch(selectedArea, (value) => {
      const area = areaList.value.find(item => item.value === value)
      if (area) {
        areaName.value = area.label
      } else {
        areaName.value = ''
      }
    })

    const open = () => {
      if (props.disabled) return
      visible.value = true
      initData()
    }

    const close = () => {
      visible.value = false
    }

    const handleConfirm = () => {
      if (isValid.value) {
        const result = {
          province: provinceName.value,
          province_id: selectedProvince.value,
          city: cityName.value,
          city_id: selectedCity.value,
          area: areaName.value,
          area_id: selectedArea.value
        }

        emit('confirm', result)
        emit('update:modelValue', result)
        close()
      }
    }

    return {
      visible,
      selectedProvince,
      selectedCity,
      selectedArea,
      provinceList,
      cityList,
      areaList,
      isValid,
      open,
      close,
      handleProvinceChange,
      handleCityChange,
      handleConfirm
    }
  },
})
</script>

<style lang="scss" scoped>
.popover-area {
  display: inline-block;
}
</style>

<style>
/* 确保下拉框在弹窗中正常工作 */
.select-dropdown-in-popover {
  z-index: 99999 !important; /* 确保下拉框在弹窗上方显示 */
}

/* 全局样式，确保所有在弹窗中的下拉框都能正常显示 */
.el-select__popper {
  z-index: 99999 !important;
}

.el-select-dropdown {
  z-index: 99999 !important;
}

.el-popper {
  z-index: 99999 !important;
}
</style>