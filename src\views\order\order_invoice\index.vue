<template>
    <div>
        <!-- 搜索区域 -->
        <el-card class="!border-none" shadow="never">
            <el-form :model="queryParams" inline>
                <el-form-item label="关键词">
                    <el-input 
                        class="ls-input" 
                        v-model="queryParams.keyword" 
                        placeholder="请输入订单号或手机号码" 
                        clearable
                    />
                </el-form-item>
                <el-form-item label="开票时间">
                    <data-picker 
                        class="ls-input" 
                        style="width: 280px" 
                        v-model:start_time="queryParams.start_time"
                        v-model:end_time="queryParams.end_time"
                    />
                </el-form-item>
                <el-form-item label="状态">
                    <el-select 
                        class="ls-input" 
                        v-model="queryParams.status" 
                        placeholder="请选择状态"
                        clearable
                    >
                        <el-option label="待开票" value="0" />
                        <el-option label="已开票" value="1" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 列表区域 -->
        <el-card class="!border-none mt-4" shadow="never">
            <el-table size="large" v-loading="pager.loading" :data="pager.lists">
                <el-table-column label="ID" prop="id" min-width="80" />
                <el-table-column label="用户头像" min-width="100">
                    <template #default="{ row }">
                        <div class="avatar-container">
                            <el-avatar
                                v-if="row.user?.avatar"
                                :src="row.user.avatar"
                                :size="50"
                                fit="cover"
                            />
                            <el-avatar
                                v-else
                                :size="50"
                                icon="el-icon-User"
                                class="default-avatar"
                            />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="昵称" min-width="120" show-overflow-tooltip>
                    <template #default="{ row }">
                        {{ row.user?.nickname || '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="手机号码" min-width="130">
                    <template #default="{ row }">
                        {{ row.user?.mobile || '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="订单号" prop="order_sn" min-width="180" show-overflow-tooltip />
                <el-table-column label="开票金额" prop="amount" min-width="120">
                    <template #default="{ row }">
                        ¥{{ row.amount }}
                    </template>
                </el-table-column>
                <el-table-column label="状态" prop="status_str" min-width="100" />
                <el-table-column label="操作" width="160" fixed="right">
                    <template #default="{ row }">
                        <el-button
                            v-perms="['order.order_invoice/detail']"
                            type="primary"
                            link
                            @click="handleDetail(row.id)"
                        >
                            详情
                        </el-button>
                        <el-button
                            v-if="row.status == 0"
                            v-perms="['order.order_invoice/invoice']"
                            type="primary"
                            link
                            @click="handleInvoice(row)"
                        >
                            开票
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="flex justify-end mt-4">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>

        <!-- 详情对话框 -->
        <el-dialog
            v-model="detailDialog.visible"
            title="订单开票详情"
            width="700px"
            @close="handleCloseDetailDialog"
        >
            <div class="detail-content">
                <!-- 订单信息 -->
                <div class="mb-4 p-3 bg-gray-50 rounded">
                    <div class="text-sm font-medium text-gray-700 mb-2">订单信息</div>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-600">开票ID：</span>
                            <span class="font-medium">{{ detailDialog.currentRow.id }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">订单号：</span>
                            <span class="font-medium">{{ detailDialog.currentRow.order_sn }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">开票金额：</span>
                            <span class="font-medium">¥{{ detailDialog.currentRow.amount }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">开票状态：</span>
                            <span class="font-medium">{{ detailDialog.currentRow.status_str }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">申请时间：</span>
                            <span class="font-medium">{{ detailDialog.currentRow.create_time }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">开票时间：</span>
                            <span class="font-medium">{{ detailDialog.currentRow.invoice_time || '-' }}</span>
                        </div>
                    </div>
                </div>

                <!-- 用户信息 -->
                <div class="mb-4 p-3 bg-gray-50 rounded">
                    <div class="text-sm font-medium text-gray-700 mb-2">用户信息</div>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-600">用户ID：</span>
                            <span class="font-medium">{{ detailDialog.currentRow.user?.id || '-' }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">用户昵称：</span>
                            <span class="font-medium">{{ detailDialog.currentRow.user?.nickname || '-' }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">手机号码：</span>
                            <span class="font-medium">{{ detailDialog.currentRow.user?.mobile || '-' }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">用户头像：</span>
                            <span class="font-medium">
                                <el-avatar
                                    v-if="detailDialog.currentRow.user?.avatar"
                                    :src="detailDialog.currentRow.user.avatar"
                                    :size="40"
                                    fit="cover"
                                />
                                <span v-else>-</span>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 企业开票信息 -->
                <div v-if="detailDialog.invoice_info.type == 2" class="mb-4 p-3 bg-gray-50 rounded">
                    <div class="text-sm font-medium text-gray-700 mb-2">企业开票信息</div>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-600">抬头类型：</span>
                            <span>{{ detailDialog.invoice_info.type == 1 ? "个人" : "企业" }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">抬头：</span>
                            <span>{{ detailDialog.invoice_info.company_name || '-' }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">纳税人识别号：</span>
                            <span>{{ detailDialog.invoice_info.tax_id || '-' }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">注册地址：</span>
                            <span>{{ detailDialog.invoice_info.reg_address || '-' }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">注册电话：</span>
                            <span>{{ detailDialog.invoice_info.reg_phone || '-' }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">开户银行：</span>
                            <span>{{ detailDialog.invoice_info.bank_name || '-' }}</span>
                        </div>
                        <div class="col-span-2">
                            <span class="text-gray-600">银行账号：</span>
                            <span>{{ detailDialog.invoice_info.bank_account || '-' }}</span>
                        </div>
                        <div class="col-span-2">
                            <span class="text-gray-600">联系邮箱：</span>
                            <span>{{ detailDialog.invoice_info.contact_email || '-' }}</span>
                        </div>
                    </div>
                </div>

                <!-- 个人开票信息 -->
                <div v-if="detailDialog.invoice_info.type == 1" class="mb-4 p-3 bg-gray-50 rounded">
                    <div class="text-sm font-medium text-gray-700 mb-2">个人开票信息</div>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-600">抬头类型：</span>
                            <span>{{ detailDialog.invoice_info.type == 1 ? "个人" : "企业" }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">抬头：</span>
                            <span>{{ detailDialog.invoice_info.company_name || '-' }}</span>
                        </div>
                        <div class="col-span-2">
                            <span class="text-gray-600">联系邮箱：</span>
                            <span>{{ detailDialog.invoice_info.contact_email || '-' }}</span>
                        </div>
                    </div>
                </div>

                <!-- 开票图片 -->
                <div v-if="detailDialog.currentRow.invoice_image && detailDialog.currentRow.invoice_image.length > 0" class="mb-4 p-3 bg-gray-50 rounded">
                    <div class="text-sm font-medium text-gray-700 mb-2">开票图片</div>
                    <div class="text-sm">
                        <div class="image-gallery">
                            <el-image
                                v-for="(img, index) in detailDialog.currentRow.invoice_image"
                                :key="index"
                                :src="img"
                                :preview-src-list="detailDialog.currentRow.invoice_image"
                                fit="cover"
                                class="gallery-img"
                                :z-index="99999"
                                :preview-teleported="true"
                            />
                        </div>
                        <div class="text-gray-500 mt-2">
                            共 {{ detailDialog.currentRow.invoice_image.length }} 张图片
                        </div>
                    </div>
                </div>

                <!-- 备注信息 -->
                <div v-if="detailDialog.currentRow.remark" class="mb-4 p-3 bg-gray-50 rounded">
                    <div class="text-sm font-medium text-gray-700 mb-2">备注信息</div>
                    <div class="text-sm text-gray-600">{{ detailDialog.currentRow.remark }}</div>
                </div>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="detailDialog.visible = false">关闭</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 开票对话框 -->
        <el-dialog
            v-model="invoiceDialog.visible"
            title="开票"
            width="600px"
            @close="handleCloseDialog"
        >
            <div class="invoice-content">
                <!-- 订单信息 -->
                <div class="mb-4 p-3 bg-gray-50 rounded">
                    <div class="text-sm font-medium text-gray-700 mb-2">订单信息</div>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-600">订单号：</span>
                            <span class="font-medium">{{ invoiceDialog.currentRow.order_sn }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">开票金额：</span>
                            <span class="font-medium">¥{{ invoiceDialog.currentRow.amount }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">用户：</span>
                            <span class="font-medium">{{ invoiceDialog.currentRow.user?.nickname }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">手机号码：</span>
                            <span class="font-medium">{{ invoiceDialog.currentRow.user?.mobile }}</span>
                        </div>
                    </div>
                </div>

                <!-- 企业开票信息 -->
                <div v-if="invoiceDialog.invoice_info.type == 2" class="mb-4 p-3 bg-gray-50 rounded">
                    <div class="text-sm font-medium text-gray-700 mb-2">企业开票信息</div>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-600">抬头类型：</span>
                            <span>{{ invoiceDialog.invoice_info.type == 1 ? "个人" : "企业" }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">抬头：</span>
                            <span>{{ invoiceDialog.invoice_info.company_name || '-' }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">纳税人识别号：</span>
                            <span>{{ invoiceDialog.invoice_info.tax_id || '-' }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">注册地址：</span>
                            <span>{{ invoiceDialog.invoice_info.reg_address || '-' }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">注册电话：</span>
                            <span>{{ invoiceDialog.invoice_info.reg_phone || '-' }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">开户银行：</span>
                            <span>{{ invoiceDialog.invoice_info.bank_name || '-' }}</span>
                        </div>
                        <div class="col-span-2">
                            <span class="text-gray-600">银行账号：</span>
                            <span>{{ invoiceDialog.invoice_info.bank_account || '-' }}</span>
                        </div>
                        <div class="col-span-2">
                            <span class="text-gray-600">联系邮箱：</span>
                            <span>{{ invoiceDialog.invoice_info.contact_email || '-' }}</span>
                        </div>
                    </div>
                </div>

                <!-- 个人开票信息 -->
                <div v-if="invoiceDialog.invoice_info.type == 1" class="mb-4 p-3 bg-gray-50 rounded">
                    <div class="text-sm font-medium text-gray-700 mb-2">个人开票信息</div>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-600">抬头类型：</span>
                            <span>{{ invoiceDialog.invoice_info.type == 1 ? "个人" : "企业" }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">抬头：</span>
                            <span>{{ invoiceDialog.invoice_info.company_name || '-' }}</span>
                        </div>
                        <div class="col-span-2">
                            <span class="text-gray-600">联系邮箱：</span>
                            <span>{{ invoiceDialog.invoice_info.contact_email || '-' }}</span>
                        </div>
                    </div>
                </div>

                <!-- 开票图片 -->
                <div class="mb-4 p-3 bg-gray-50 rounded">
                    <div class="text-sm font-medium text-gray-700 mb-2">开票图片</div>
                    <div class="text-sm">
                        <material-picker
                            v-model="invoiceDialog.invoice_image"
                            :limit="10"
                            type="image"
                            multiple
                        />
                        <div class="text-gray-500 mt-2">
                            支持多张图片上传，最多10张
                        </div>
                    </div>
                </div>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="invoiceDialog.visible = false">取消</el-button>
                    <el-button type="primary" @click="handleConfirmInvoice" :loading="invoiceDialog.loading">
                        确认开票
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup name="orderInvoiceIndex">
import { apiOrderInvoiceLists, apiOrderInvoiceInvoice, apiOrderInvoiceDetail } from '@/api/order/order_invoice'
import { usePaging } from '@/hooks/usePaging'

const queryParams = reactive({
    keyword: '',
    start_time: '',
    end_time: '',
    status: ''
})

const { pager, getLists, resetParams, resetPage } = usePaging({
    fetchFun: apiOrderInvoiceLists,
    params: queryParams
})

// 详情对话框
const detailDialog = reactive({
    visible: false,
    currentRow: {} as any,
    invoice_info: {} as any
})

// 开票对话框
const invoiceDialog = reactive({
    visible: false,
    loading: false,
    currentRow: {} as any,
    invoice_image: [] as string[],
    invoice_info: {} as any
})

// 查看详情
const handleDetail = async (id: number) => {
    try {
        const data = await apiOrderInvoiceDetail({ id })
        detailDialog.currentRow = data
        detailDialog.invoice_info = data.invoice_info || {}
        detailDialog.visible = true
    } catch (error) {
        console.error('获取详情失败:', error)
    }
}

// 关闭详情对话框
const handleCloseDetailDialog = () => {
    detailDialog.visible = false
    detailDialog.currentRow = {}
    detailDialog.invoice_info = {}
}

// 打开开票对话框
const handleInvoice = (row: any) => {
    invoiceDialog.currentRow = { ...row }
    invoiceDialog.invoice_info = row.invoice_info || {}
    invoiceDialog.invoice_image = []
    invoiceDialog.visible = true
}

// 关闭对话框
const handleCloseDialog = () => {
    invoiceDialog.visible = false
    invoiceDialog.currentRow = {}
    invoiceDialog.invoice_info = {}
    invoiceDialog.invoice_image = []
    invoiceDialog.loading = false
}

// 确认开票
const handleConfirmInvoice = async () => {
    if (invoiceDialog.invoice_image.length === 0) {
        ElMessage.warning('请上传开票图片')
        return
    }
    
    try {
        invoiceDialog.loading = true
        await apiOrderInvoiceInvoice({
            id: invoiceDialog.currentRow.id,
            invoice_image: invoiceDialog.invoice_image
        })
        ElMessage.success('开票成功')
        handleCloseDialog()
        getLists() // 刷新列表
    } catch (error) {
        console.error('开票失败:', error)
    } finally {
        invoiceDialog.loading = false
    }
}

onMounted(() => {
    getLists()
})
</script>

<style lang="scss" scoped>
.ls-input {
    width: 280px;
}

.avatar-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    
    .default-avatar {
        background-color: #f5f7fa;
        color: #909399;
    }
}

.invoice-content {
    padding: 10px 0;
}

.dialog-footer {
    text-align: right;
}

.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 12px;

    .gallery-img {
        width: 80px;
        height: 80px;
        border-radius: 6px;
        cursor: pointer;
        border: 1px solid #e4e7ed;
    }
}

// 修复图片预览层级问题
:deep(.el-image-viewer__wrapper) {
    z-index: 99999 !important;
}

:deep(.el-image__preview) {
    z-index: 99999 !important;
}
</style>
