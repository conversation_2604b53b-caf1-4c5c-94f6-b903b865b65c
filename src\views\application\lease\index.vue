<template>
    <div>
        <!-- 搜索区域 -->
        <el-card class="!border-none" shadow="never">
            <el-form :model="queryParams" inline>
                <el-form-item label="关键词">
                    <el-input 
                        class="ls-input" 
                        v-model="queryParams.keyword" 
                        placeholder="请输入联系人或手机号码" 
                        clearable
                    />
                </el-form-item>
                <el-form-item label="状态">
                    <el-select 
                        class="ls-input" 
                        v-model="queryParams.status" 
                        placeholder="请选择状态"
                        clearable
                    >
                        <el-option label="全部" value="" />
                        <el-option label="待联系" value="0" />
                        <el-option label="已租用" value="1" />
                        <el-option label="已关闭" value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item label="添加时间">
                    <data-picker 
                        class="ls-input" 
                        style="width: 280px" 
                        v-model:start_time="queryParams.start_time"
                        v-model:end_time="queryParams.end_time"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 列表区域 -->
        <el-card class="!border-none mt-4" shadow="never">
            <el-table size="large" v-loading="pager.loading" :data="pager.lists">
                <el-table-column label="ID" prop="id" min-width="80" />
                <el-table-column label="联系人" prop="name" min-width="120" show-overflow-tooltip />
                <el-table-column label="手机号码" prop="mobile" min-width="130" />
                <el-table-column label="无人机名称" prop="drone_name" min-width="180" show-overflow-tooltip />
                <el-table-column label="时长（天）" prop="duration" min-width="120">
                    <template #default="{ row }">
                        {{ row.duration }}天
                    </template>
                </el-table-column>
                <el-table-column label="状态" prop="status_str" min-width="100">
                    <template #default="{ row }">
                        <el-tag :type="getStatusType(row.status)">
                            {{ row.status_str }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="添加时间" prop="create_time" min-width="180" />
                <el-table-column label="操作" width="200" fixed="right">
                    <template #default="{ row }">
                        <el-button
                            v-perms="['drone.lease/detail']"
                            type="primary"
                            link
                            @click="handleDetail(row.id)"
                        >
                            详情
                        </el-button>
                        <el-button
                            v-perms="['drone.lease/enable']"
                            v-if="row.status == 0"
                            type="success"
                            link
                            @click="handleStatus(row.id, 1)"
                        >
                            出租
                        </el-button>
                        <el-button
                            v-perms="['drone.lease/close']"
                            v-if="row.status == 0"
                            type="danger"
                            link
                            @click="handleStatus(row.id, 2)"
                        >
                            关闭
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="flex justify-end mt-4">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>
    </div>
</template>

<script lang="ts" setup name="leaseIndex">
import { apiLeaseLists, apiLeaseStatus } from '@/api/marketing/lease'
import { usePaging } from '@/hooks/usePaging'
import feedback from '@/utils/feedback'

const router = useRouter()

const queryParams = reactive({
    keyword: '',
    status: '',
    start_time: '',
    end_time: ''
})

const { pager, getLists, resetParams, resetPage } = usePaging({
    fetchFun: apiLeaseLists,
    params: queryParams
})

// 获取状态类型（与无人机列表页面保持一致）
const getStatusType = (status: number) => {
    switch (status) {
        case 0: return ''         // 待联系 - 默认样式
        case 1: return 'success'  // 已出租 - 绿色
        case 2: return 'danger'   // 已关闭 - 红色
        default: return ''
    }
}

// 查看详情
const handleDetail = (id: number) => {
    router.push({
        path: '/application/lease/detail',
        query: { id }
    })
}

// 状态操作（出租/关闭）
const handleStatus = async (id: number, status: number) => {
    const actionText = status === 1 ? '出租' : '关闭'
    await feedback.confirm(`确定要${actionText}这个租赁申请吗？`)
    await apiLeaseStatus({ id, status })
    getLists()
}

onMounted(() => {
    getLists()
})
</script>

<style lang="scss" scoped>
.ls-input {
    width: 280px;
}
</style>
