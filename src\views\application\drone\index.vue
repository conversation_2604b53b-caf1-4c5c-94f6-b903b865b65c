<template>
    <div>
        <!-- 搜索区域 -->
        <el-card class="!border-none" shadow="never">
            <el-form :model="queryParams" inline>
                <el-form-item label="标题">
                    <el-input 
                        class="ls-input" 
                        v-model="queryParams.title" 
                        placeholder="请输入标题" 
                        clearable
                    />
                </el-form-item>
                <el-form-item label="状态">
                    <el-select 
                        class="ls-input" 
                        v-model="queryParams.status" 
                        placeholder="请选择状态"
                        clearable
                    >
                        <el-option label="全部" value="" />
                        <el-option label="上架" value="1" />
                        <el-option label="下架" value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item label="添加时间">
                    <data-picker 
                        class="ls-input" 
                        style="width: 280px" 
                        v-model:start_time="queryParams.start_time"
                        v-model:end_time="queryParams.end_time"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 列表区域 -->
        <el-card class="!border-none mt-4" shadow="never">
            <div>
                <router-link
                    v-perms="['drone.lists/add']"
                    :to="{
                        path: '/application/drone/edit'
                    }"
                >
                    <el-button type="primary" class="mb-4">
                        <template #icon>
                            <icon name="el-icon-Plus" />
                        </template>
                        添加无人机
                    </el-button>
                </router-link>
            </div>
            <el-table size="large" v-loading="pager.loading" :data="pager.lists">
                <el-table-column label="ID" prop="id" min-width="80" />
                <el-table-column label="图片" min-width="100">
                    <template #default="{ row }">
                        <image-contain
                            v-if="row.images && row.images.length > 0"
                            :src="row.images[0]"
                            :width="60"
                            :height="60"
                            :preview-src-list="row.images"
                            fit="contain"
                        />
                        <div v-else class="image-slot">
                            <el-icon><Picture /></el-icon>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="标题" prop="title" min-width="150" show-overflow-tooltip />
                <el-table-column label="品牌" prop="brand" min-width="120" show-overflow-tooltip />
                <el-table-column label="押金" prop="deposit" min-width="100">
                    <template #default="{ row }">
                        ¥{{ row.deposit }}
                    </template>
                </el-table-column>
                <el-table-column label="租用单价" prop="price" min-width="100">
                    <template #default="{ row }">
                        ¥{{ row.price }}
                    </template>
                </el-table-column>
                <el-table-column label="状态" prop="status_str" min-width="100">
                    <template #default="{ row }">
                        <el-tag :type="row.status == 1 ? 'success' : 'danger'">
                            {{ row.status_str }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="排序" prop="sort" min-width="80" />
                <el-table-column label="添加时间" prop="create_time" min-width="180" />
                <el-table-column label="操作" width="200" fixed="right">
                    <template #default="{ row }">
                        <el-button
                            v-perms="['drone.lists/edit']"
                            type="primary"
                            link
                            @click="handleEdit(row.id)"
                        >
                            编辑
                        </el-button>
                        <el-button
                            v-perms="['drone.lists/edit']"
                            :type="row.status == 1 ? 'warning' : 'success'"
                            link
                            @click="handleStatus(row.id, row.status)"
                        >
                            {{ row.status == 1 ? '下架' : '上架' }}
                        </el-button>
                        <el-button
                            v-perms="['drone.lists/del']"
                            type="danger"
                            link
                            @click="handleDelete(row.id)"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="flex justify-end mt-4">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>
    </div>
</template>

<script lang="ts" setup name="droneIndex">
import { apiDroneLists, apiDroneDelete, apiDroneStatus } from '@/api/marketing/drone'
import { usePaging } from '@/hooks/usePaging'
import feedback from '@/utils/feedback'
import { Picture } from '@element-plus/icons-vue'

const router = useRouter()

const queryParams = reactive({
    title: '',
    status: '',
    start_time: '',
    end_time: ''
})

const { pager, getLists, resetParams, resetPage } = usePaging({
    fetchFun: apiDroneLists,
    params: queryParams
})

// 编辑
const handleEdit = (id: number) => {
    router.push({
        path: '/application/drone/edit',
        query: { id }
    })
}

// 删除
const handleDelete = async (id: number) => {
    await feedback.confirm('确定要删除这个无人机吗？')
    await apiDroneDelete({ id })
    getLists()
}

// 上/下架
const handleStatus = async (id: number, status: number) => {
    const newStatus = status == 1 ? 0 : 1  // 切换状态：1->0(下架), 0->1(上架)
    const action = status == 1 ? '下架' : '上架'
    await feedback.confirm(`确定要${action}这个无人机吗？`)
    await apiDroneStatus({ id, status: newStatus })
    getLists()
}

onMounted(() => {
    getLists()
})
</script>

<style lang="scss" scoped>
.image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 60px;
    height: 60px;
    background: var(--el-fill-color-light);
    color: var(--el-text-color-secondary);
    font-size: 20px;
    border-radius: 4px;
}

.ls-input {
    width: 280px;
}
</style>
