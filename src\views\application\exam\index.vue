<template>
    <div>
        <!-- 搜索区域 -->
        <el-card class="!border-none" shadow="never">
            <el-form :model="queryParams" inline>
                <el-form-item label="关键词">
                    <el-input 
                        class="ls-input" 
                        v-model="queryParams.keyword" 
                        placeholder="请输入联系人或手机号码" 
                        clearable
                    />
                </el-form-item>
                <el-form-item label="支付时间">
                    <data-picker 
                        class="ls-input" 
                        style="width: 280px" 
                        v-model:start_time="queryParams.start_time"
                        v-model:end_time="queryParams.end_time"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 列表区域 -->
        <el-card class="!border-none mt-4" shadow="never">
            <el-table size="large" v-loading="pager.loading" :data="pager.lists">
                <el-table-column label="ID" prop="id" min-width="80" />
                <el-table-column label="联系人" prop="name" min-width="120" show-overflow-tooltip />
                <el-table-column label="手机号码" prop="mobile" min-width="130" />
                <el-table-column label="培训城市" prop="train_city_name" min-width="120" show-overflow-tooltip />
                <el-table-column label="培训类目" prop="train_cate" min-width="150" show-overflow-tooltip />
                <el-table-column label="付款金额" prop="price" min-width="120">
                    <template #default="{ row }">
                        ¥{{ row.price }}
                    </template>
                </el-table-column>
                <el-table-column label="付款时间" prop="pay_time_str" min-width="180" />
                <el-table-column label="操作" width="120" fixed="right">
                    <template #default="{ row }">
                        <el-button
                            v-perms="['user.exam/detail']"
                            type="primary"
                            link
                            @click="handleDetail(row.id)"
                        >
                            详情
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="flex justify-end mt-4">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>
    </div>
</template>

<script lang="ts" setup name="examIndex">
import { apiUserExamLists } from '@/api/marketing/user_exam'
import { usePaging } from '@/hooks/usePaging'

const router = useRouter()

const queryParams = reactive({
    keyword: '',
    start_time: '',
    end_time: ''
})

const { pager, getLists, resetParams, resetPage } = usePaging({
    fetchFun: apiUserExamLists,
    params: queryParams
})

// 查看详情
const handleDetail = (id: number) => {
    router.push({
        path: '/application/exam/detail',
        query: { id }
    })
}

onMounted(() => {
    getLists()
})
</script>

<style lang="scss" scoped>
.ls-input {
    width: 280px;
}
</style>
