<template>
    <el-card shadow="never" class="!border-none">
        <el-form :model="formData" inline>
            <el-form-item label="ID">
                <el-input class="ls-input" v-model="formData.id" placeholder="飞手ID" />
            </el-form-item>
            <el-form-item label="飞手信息">
                <el-input class="ls-input" v-model="formData.staff_info" placeholder="昵称/手机号码" />
            </el-form-item>
            <el-form-item label="工作状态">
                <el-select v-model="formData.work_status" placeholder="全部" class="ls-input">
                    <el-option label="全部" value></el-option>
                    <el-option label="接单中" value="1"></el-option>
                    <el-option label="休息中" value="0"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="服务状态">
                <el-select v-model="formData.status" placeholder="全部" class="ls-input">
                    <el-option label="全部" value></el-option>
                    <el-option label="正常" value="1"></el-option>
                    <el-option label="冻结" value="0"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="所属地区">
                <area-select v-model:province="formData.province_id" v-model:city="formData.city_id"
                    v-model:district="formData.district_id" />
            </el-form-item>
            <el-form-item label="所属团队">
                <el-select v-model="formData.team_id" filterable remote reserve-keyword placeholder="请输入团队名称"
                    :remote-method="remoteMethod" :loading="loading" class="ls-input">
                    <el-option v-for="item in teamLists" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
            </el-form-item>
            <!-- <el-form-item label="服务技能">
                <el-select v-model="formData.skill_id" placeholder="全部" class="ls-input">
                    <el-option label="全部" value></el-option>
                    <el-option
                        v-for="item in skillData"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item> -->
            <el-form-item label="注册时间">
                <data-picker class="ls-input" style="width: 280px" v-model:start_time="formData.start_time"
                    v-model:end_time="formData.end_time"></data-picker>
            </el-form-item>
            <el-form-item>
                <div class="flex">
                    <el-button type="primary" @click="getLists">查询</el-button>
                    <el-button @click="handleResetParams">重置</el-button>
                    <el-button @click="handleExport">导出</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-card>

    <el-card shadow="never" class="mt-4 !border-none">
        <!-- <el-button type="primary" @click="$router.push('lists/edit')" v-perms="['staff.staff/add']">新增飞手</el-button> -->
        <div class="mt-3">
            <el-table :data="pager.lists" style="width: 100%" v-loading="pager.loading">
                <el-table-column prop="id" label="ID" min-width="120"></el-table-column>
                <el-table-column label="飞手信息" min-width="200">
                    <template #default="scope">
                        <div class="flex col-center items-center">
                            <div class="w-[60px] h-[60px]">
                                <el-image style="width: 60px; height: 60px" :src="scope.row.work_image"
                                    :preview-src-list="[scope.row.work_image]" :hide-on-click-modal="true"
                                    :preview-teleported="true" :fit="'cover'" />
                            </div>
                            <div class="ml-[10px]">
                                <div class="truncate">
                                    <span>{{ scope.row.name }}</span>
                                    <el-tag class="ml-[5px]" :type="scope.row.work_status ? 'success' : 'info'"
                                        size="small">{{ scope.row.work_status_desc }}</el-tag>
                                </div>
                                <!-- <div class="text-sm text-[#909399]">工号：{{ scope.row.sn }}</div> -->
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="手机号码" min-width="120">
                    <template #default="scope">
                        <div>{{ scope.row.mobile }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="无人机数量" min-width="120">
                    <template #default="scope">
                        <div>{{ scope.row.drone_num }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="性别" min-width="120">
                    <template #default="scope">
                        <div>{{ scope.row.sex_desc }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="所属团队" min-width="120">
                    <template #default="scope">
                        <div>{{ scope.row.team?.name || '-' }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="所属地区" min-width="200">
                    <template #default="scope">
                        <div>{{ scope.row.province }} {{ scope.row.city }} {{ scope.row.district }}</div>
                    </template>
                </el-table-column>
                <!-- <el-table-column label="接单数据" min-width="200">
                    <template #default="scope">
                        <div>每日接单：{{ scope.row.deposit_info.order_num }}</div>
                        <div>累计接单：{{ scope.row.total_order_num }}</div>
                    </template>
                </el-table-column> -->
                <el-table-column property="status_desc" label="状态" min-width="80" />
                <!-- <el-table-column property="sort" label="排序" min-width="80" /> -->
                <el-table-column property="create_time" label="注册时间" min-width="180" />
                <el-table-column label="操作" min-width="200" fixed="right">
                    <template #default="scope">
                        <div class="flex">
                            <router-link v-perms="['staff.staff/detail']" class="mr-2" :to="{
                                path: 'lists/detail',
                                query: {
                                    id: scope.row.id
                                }
                            }">
                                <el-link type="primary" :underline="false">详情</el-link>
                            </router-link>
                            <!-- <a v-perms="['staff.staff/export']" class="mr-2" :href="getExportUrl(scope.row.id)" target="_blank">
                                <el-link type="primary" :underline="false">导出</el-link>
                            </a> -->
                            <el-link
                                v-perms="['staff.staff/status']"
                                :type="scope.row.status == 1 ? 'danger' : 'success'"
                                :underline="false"
                                @click="handleStatusChange(scope.row)"
                            >
                                {{ scope.row.status == 1 ? '禁用' : '启用' }}
                            </el-link>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="flex justify-end mt-4">
            <pagination v-model="pager" @change="getLists" />
        </div>
    </el-card>
</template>

<script lang="ts" setup name="masterWorkerLists">
import {
    apiMasterWorkerLists,
    apiMasterWorkerDel,
    apiMasterWorkerStatusEdit
} from '@/api/master_worker'
import { ref, shallowRef, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import Pagination from '@/components/pagination/index.vue'
import DataPicker from '@/components/data-picker/index.vue'
import AreaSelect from '@/components/area-select/index.vue'
import feedback from '@/utils/feedback'
import { usePaging } from '@/hooks/usePaging'
import { apiAllStaffSkillLists } from '@/api/master_worker/skill'
import { getAllTeamLists } from '@/api/master_worker/team'
import { getToken } from '@/utils/auth'

interface formDataObj {
    staff_info?: string
    start_time: string
    end_time: string
    work_status: number | string
    skill_id: number | string | undefined
    status: number | string
    team_id?: number | string
    province_id?: number | string
    city_id?: number | string
    district_id?: number | string
}

const route = useRoute()
const formData = ref<formDataObj>({
    staff_info: '',
    start_time: '',
    end_time: '',
    work_status: '',
    skill_id: '',
    status: '',
    team_id: '',
    province_id: '',
    city_id: '',
    district_id: ''
})
const skillData = ref<Array<object>>([])
const teamLists = ref<any[]>([])
const loading = ref(false)


const { pager, getLists, resetPage, resetParams } = usePaging({
    fetchFun: apiMasterWorkerLists,
    params: formData.value
})

// 重置筛选框
const handleResetParams = () => {
    resetParams()
}

// 生成导出链接
const getExportUrl = (id: number | string) => {
    // 这里您可以根据实际的导出接口地址进行修改
    return `/api/staff.staff/export?id=${id}`
}

// 处理状态切换
const handleStatusChange = async (row: any) => {
    const newStatus = row.status == 1 ? 0 : 1
    const statusText = newStatus == 1 ? '启用' : '禁用'

    try {
        await feedback.confirm(`确定要${statusText}该飞手吗？`)
        await apiMasterWorkerStatusEdit({
            id: row.id,
            status: newStatus
        })
        // feedback.msgSuccess(`${statusText}成功`)
        getLists() // 刷新列表
    } catch (error) {
        console.error('状态切换失败:', error)
    }
}

// 获取技能通用列表
const getStaffLists = async (): Promise<void> => {
    const res = await apiAllStaffSkillLists({})
    skillData.value = res
}

// 标记是否已加载过所有团队数据
const hasLoadedAllTeams = ref(false)

// 获取所有团队列表数据
const getTeamLists = () => {
    if (hasLoadedAllTeams.value) return Promise.resolve(teamLists.value)

    loading.value = true
    return getAllTeamLists().then((res) => {
        teamLists.value = res
        loading.value = false
        hasLoadedAllTeams.value = true
        return res
    })
}

const remoteMethod = (query: string) => {
    if (query) {
        loading.value = true
        getAllTeamLists({ name: query }).then((res) => {
            teamLists.value = res
            loading.value = false
        })
    } else if (hasLoadedAllTeams.value) {
        // 如果已经加载过所有团队数据，直接使用缓存的数据
        // 不需要重新请求
    } else {
        // 如果还没有加载过所有团队数据，则加载
        getTeamLists()
    }
}

// 处理导出功能
const handleExport = () => {
    // 构建查询参数
    const queryParams = new URLSearchParams()

    // 直接遍历formData对象，添加所有非空参数
    Object.entries(formData.value).forEach(([key, value]) => {
        if (value !== '' && value !== null && value !== undefined) {
            queryParams.append(key, value.toString())
        }
    })

    // 添加版本号
    queryParams.append("version", "1")

    // 添加token
    const token = getToken()
    if (token) {
        queryParams.append("token", token)
    }

    // 构建完整的导出URL
    const exportUrl = `/adminapi/staff.staff/export?${queryParams.toString()}`

    // 使用window.open打开导出链接
    window.open(exportUrl, '_blank')
}

onMounted(async () => {
    const { team_id } = route.query

    if (team_id) {
        // 如果有team_id参数，先专门获取这个团队的详细信息
        loading.value = true
        try {
            // 确保使用正确的ID格式查询
            const teamRes = await getAllTeamLists({ id: team_id })
            if (teamRes && teamRes.length > 0) {
                // 先将获取到的团队信息设置到团队列表中
                teamLists.value = teamRes

                // 获取团队的实际ID（可能是数字类型）
                const actualTeamId = teamRes[0].id

                // 使用实际ID设置选中的团队
                formData.value.team_id = actualTeamId

                // 标记已加载团队数据
                hasLoadedAllTeams.value = true
            } else {
                // 如果没有找到指定ID的团队，加载所有团队
                await getTeamLists()
                // 尝试将字符串ID转换为数字（如果API期望数字类型）
                const numericId = parseInt(team_id as string)
                formData.value.team_id = isNaN(numericId) ? team_id : numericId
            }
        } finally {
            loading.value = false
        }
    } else {
        // 没有team_id参数，加载所有团队数据
        await getTeamLists()
    }

    getLists()
    getStaffLists()
})
</script>

<style lang="scss">
.ls-input {
    width: 280px;
}
</style>
