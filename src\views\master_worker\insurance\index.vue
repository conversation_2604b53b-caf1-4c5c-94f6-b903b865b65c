<template>
    <div>
        <!-- 搜索区域 -->
        <el-card class="!border-none" shadow="never">
            <el-form :model="queryParams" inline>
                <el-form-item label="关键词">
                    <el-input 
                        class="ls-input" 
                        v-model="queryParams.keyword" 
                        placeholder="请输入联系人或手机号码" 
                        clearable
                    />
                </el-form-item>
                <el-form-item label="添加时间">
                    <data-picker 
                        class="ls-input" 
                        style="width: 280px" 
                        v-model:start_time="queryParams.start_time"
                        v-model:end_time="queryParams.end_time"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 列表区域 -->
        <el-card class="!border-none mt-4" shadow="never">
            <el-table size="large" v-loading="pager.loading" :data="pager.lists">
                <el-table-column label="ID" prop="id" min-width="80" />
                <el-table-column label="联系人" prop="name" min-width="120" show-overflow-tooltip />
                <el-table-column label="手机号码" prop="mobile" min-width="130" />
                <el-table-column label="保险名称" prop="insurance_name" min-width="180" show-overflow-tooltip />
                <el-table-column label="保险说明" prop="remark" min-width="200" show-overflow-tooltip />
                <el-table-column label="添加时间" prop="create_time" min-width="180" />
                <el-table-column label="保单图" min-width="120">
                    <template #default="{ row }">
                        <div v-if="row.images && row.images.length > 0" class="image-preview">
                            <el-image
                                v-for="(img, index) in row.images.slice(0, 3)"
                                :key="index"
                                :src="img"
                                :preview-src-list="row.images"
                                fit="cover"
                                class="preview-img"
                                :z-index="99999"
                                :preview-teleported="true"
                            />
                            <span v-if="row.images.length > 3" class="more-count">
                                +{{ row.images.length - 3 }}
                            </span>
                        </div>
                        <span v-else class="text-gray-400">暂无图片</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="120" fixed="right">
                    <template #default="{ row }">
                        <el-button
                            v-perms="['staff.insurance/imaged']"
                            type="primary"
                            link
                            @click="handleUploadImages(row)"
                        >
                            上传保单图
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="flex justify-end mt-4">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>

        <!-- 上传保单图对话框 -->
        <el-dialog
            v-model="uploadDialog.visible"
            title="上传保单图"
            width="600px"
            @close="handleCloseDialog"
        >
            <div class="upload-content">
                <div class="mb-4">
                    <span class="text-gray-600">联系人：</span>
                    <span class="font-medium">{{ uploadDialog.currentRow.name }}</span>
                    <span class="ml-4 text-gray-600">手机号码：</span>
                    <span class="font-medium">{{ uploadDialog.currentRow.mobile }}</span>
                </div>
                <div class="mb-4">
                    <span class="text-gray-600">保险名称：</span>
                    <span class="font-medium">{{ uploadDialog.currentRow.insurance_name }}</span>
                </div>
                <div>
                    <div class="mb-2 text-gray-700">保单图片：</div>
                    <material-picker
                        v-model="uploadDialog.images"
                        :limit="20"
                        type="image"
                        multiple
                    />
                    <div class="text-sm text-gray-500 mt-2">
                        支持多张图片上传，最多20张
                    </div>
                </div>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="uploadDialog.visible = false">取消</el-button>
                    <el-button type="primary" @click="handleConfirmUpload" :loading="uploadDialog.loading">
                        确认上传
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup name="insuranceIndex">
import { apiInsuranceLists, apiInsuranceSetImg } from '@/api/marketing/insurance'
import { usePaging } from '@/hooks/usePaging'

const queryParams = reactive({
    keyword: '',
    start_time: '',
    end_time: ''
})

const { pager, getLists, resetParams, resetPage } = usePaging({
    fetchFun: apiInsuranceLists,
    params: queryParams
})

// 上传对话框
const uploadDialog = reactive({
    visible: false,
    loading: false,
    currentRow: {} as any,
    images: [] as string[]
})

// 打开上传保单图对话框
const handleUploadImages = (row: any) => {
    uploadDialog.currentRow = { ...row }
    uploadDialog.images = row.images ? [...row.images] : []
    uploadDialog.visible = true
}

// 关闭对话框
const handleCloseDialog = () => {
    uploadDialog.visible = false
    uploadDialog.currentRow = {}
    uploadDialog.images = []
    uploadDialog.loading = false
}

// 确认上传
const handleConfirmUpload = async () => {
    try {
        uploadDialog.loading = true
        await apiInsuranceSetImg({
            id: uploadDialog.currentRow.id,
            images: uploadDialog.images
        })
        ElMessage.success('保单图上传成功')
        handleCloseDialog()
        getLists() // 刷新列表
    } catch (error) {
        console.error('上传失败:', error)
    } finally {
        uploadDialog.loading = false
    }
}

onMounted(() => {
    getLists()
})
</script>

<style lang="scss" scoped>
.ls-input {
    width: 280px;
}

.image-preview {
    display: flex;
    align-items: center;
    gap: 4px;

    .preview-img {
        width: 40px;
        height: 40px;
        border-radius: 4px;
        cursor: pointer;
    }

    .more-count {
        font-size: 12px;
        color: #909399;
        margin-left: 4px;
    }
}

.upload-content {
    padding: 10px 0;
}

.dialog-footer {
    text-align: right;
}

// 修复图片预览层级问题
:deep(.el-image-viewer__wrapper) {
    z-index: 99999 !important;
}

:deep(.el-image__preview) {
    z-index: 99999 !important;
}

// 确保表格行不会覆盖预览
:deep(.el-table__row) {
    position: relative;
    z-index: 1;
}

// 确保表格操作列不会覆盖预览
:deep(.el-table__fixed-right) {
    z-index: 2;
}

// 图片预览时，降低表格层级
:deep(.el-image-viewer__wrapper) ~ * {
    z-index: 1 !important;
}
</style>
