<template>
    <div>
        <!-- Header Start -->
        <el-card class="!border-none" shadow="never">
            <el-page-header :content="pageTitle" @back="$router.back()" />
        </el-card>
        <!-- Header End -->

        <div class="exam-detail-main">
            <!-- 考试信息 Start -->
            <el-card class="!border-none mt-4" shadow="never" style="padding: 0 20px">
                <template #header>
                    <div class="card-header">
                        <span class="font-medium nr">考试信息</span>
                    </div>
                </template>
                <!-- 考试信息 -->
                <div v-loading="loading" class="detail-grid">
                    <div class="detail-item">
                        <span class="detail-label">考试ID:</span>
                        <span class="detail-value">{{ detailData.id || '-' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">联系人:</span>
                        <span class="detail-value">{{ detailData.name || '-' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">手机号码:</span>
                        <span class="detail-value">{{ detailData.mobile || '-' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">所在城市:</span>
                        <span class="detail-value">{{ detailData.city_name || '-' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">培训城市:</span>
                        <span class="detail-value">{{ detailData.train_city_name || '-' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">培训类目:</span>
                        <span class="detail-value">{{ detailData.train_cate || '-' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">期望培训时间:</span>
                        <span class="detail-value">{{ detailData.train_time_str || '-' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">付款金额:</span>
                        <span class="detail-value">¥{{ detailData.price || '0.00' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">培训课程:</span>
                        <span class="detail-value">{{ detailData.course.name || '-' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">付款时间:</span>
                        <span class="detail-value">{{ detailData.pay_time_str || '-' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">申请时间:</span>
                        <span class="detail-value">{{ detailData.create_time || '-' }}</span>
                    </div>
                </div>
            </el-card>
            <!-- 考试信息 End -->

            <!-- 其他信息 Start -->
            <el-card class="!border-none mt-4" shadow="never" style="padding: 0 20px" v-if="detailData.remark">
                <template #header>
                    <span class="font-medium nr">备注信息</span>
                </template>
                <div class="p-4">
                    <div class="text-gray-600">{{ detailData.remark }}</div>
                </div>
            </el-card>
            <!-- 其他信息 End -->
        </div>
    </div>
</template>

<script lang="ts" setup name="examDetail">
import { apiUserExamDetail } from '@/api/marketing/user_exam'

const route = useRoute()

const pageTitle = '用户考试详情'
const loading = ref(false)

const detailData = reactive({
    id: '',
    name: '',
    mobile: '',
    city_name: '',
    train_city_name: '',
    train_cate: '',
    train_time_str: '',
    price: '',
    pay_time_str: '',
    create_time: '',
    update_time: '',
    remark: '',
    course: {} as any
})

// 获取详情
const getDetail = async () => {
    loading.value = true
    try {
        const data = await apiUserExamDetail({
            id: route.query.id
        })
        Object.keys(detailData).forEach((key) => {
            //@ts-ignore
            detailData[key] = data[key]
        })
    } finally {
        loading.value = false
    }
}

onMounted(() => {
    if (route.query.id) {
        getDetail()
    }
})
</script>

<style lang="scss">
.exam-detail-main .el-card__header,
.exam-detail-main .el-card__body {
    padding: calc(var(--el-card-padding) - 2px) 0;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px 40px;
    padding: 20px 0;
}

.detail-item {
    display: flex;
    align-items: center;

    .detail-label {
        font-weight: 500;
        color: #606266;
        margin-right: 12px;
        min-width: 80px;
        flex-shrink: 0;
    }

    .detail-value {
        color: #303133;
        word-break: break-all;
        flex: 1;
    }
}

// 响应式布局
@media (max-width: 1200px) {
    .detail-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .detail-grid {
        grid-template-columns: 1fr;
    }
}
</style>
