<template>
    <div>
        <!-- 搜索区域 -->
        <el-card class="!border-none" shadow="never">
            <el-form :model="queryParams" inline>
                <el-form-item label="关键词">
                    <el-input 
                        class="ls-input" 
                        v-model="queryParams.keyword" 
                        placeholder="请输入昵称或手机号码" 
                        clearable
                    />
                </el-form-item>
                <el-form-item label="添加时间">
                    <data-picker 
                        class="ls-input" 
                        style="width: 280px" 
                        v-model:start_time="queryParams.start_time"
                        v-model:end_time="queryParams.end_time"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 列表区域 -->
        <el-card class="!border-none mt-4" shadow="never">
            <el-table size="large" v-loading="pager.loading" :data="pager.lists">
                <el-table-column label="ID" prop="id" min-width="80" />
                <el-table-column label="用户头像" min-width="100">
                    <template #default="{ row }">
                        <div class="avatar-container">
                            <el-avatar
                                v-if="row.user?.avatar"
                                :src="row.user.avatar"
                                :size="50"
                                fit="cover"
                            />
                            <el-avatar
                                v-else
                                :size="50"
                                icon="el-icon-User"
                                class="default-avatar"
                            />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="昵称" min-width="120" show-overflow-tooltip>
                    <template #default="{ row }">
                        {{ row.user?.nickname || '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="手机号码" min-width="130">
                    <template #default="{ row }">
                        {{ row.user?.mobile || '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="提交内容" min-width="300" show-overflow-tooltip>
                    <template #default="{ row }">
                        <div class="content-cell">
                            <el-tooltip
                                v-if="row.content && row.content.length > 50"
                                :content="row.content"
                                placement="top"
                                effect="dark"
                            >
                                <span>{{ row.content.substring(0, 50) }}...</span>
                            </el-tooltip>
                            <span v-else>{{ row.content || '-' }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="图片" min-width="120">
                    <template #default="{ row }">
                        <div v-if="row.image && row.image.length > 0" class="image-preview">
                            <el-image
                                v-for="(img, index) in row.image.slice(0, 3)"
                                :key="index"
                                :src="img"
                                :preview-src-list="row.image"
                                fit="cover"
                                class="preview-img"
                                :z-index="99999"
                                :preview-teleported="true"
                            />
                            <span v-if="row.image.length > 3" class="more-count">
                                +{{ row.image.length - 3 }}
                            </span>
                        </div>
                        <span v-else class="text-gray-400">暂无图片</span>
                    </template>
                </el-table-column>
                <el-table-column label="添加时间" min-width="180">
                    <template #default="{ row }">
                        {{ row.create_time || '-' }}
                    </template>
                </el-table-column>
            </el-table>
            <div class="flex justify-end mt-4">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>
    </div>
</template>

<script lang="ts" setup name="feedbackIndex">
import { apiFeedbackLists } from '@/api/marketing/feedback'
import { usePaging } from '@/hooks/usePaging'

const queryParams = reactive({
    keyword: '',
    start_time: '',
    end_time: ''
})

const { pager, getLists, resetParams, resetPage } = usePaging({
    fetchFun: apiFeedbackLists,
    params: queryParams
})

onMounted(() => {
    getLists()
})
</script>

<style lang="scss" scoped>
.ls-input {
    width: 280px;
}

.avatar-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .default-avatar {
        background-color: #f5f7fa;
        color: #909399;
    }
}

.content-cell {
    line-height: 1.5;
    word-break: break-word;
}

.image-preview {
    display: flex;
    align-items: center;
    gap: 4px;

    .preview-img {
        width: 40px;
        height: 40px;
        border-radius: 4px;
        cursor: pointer;
    }

    .more-count {
        font-size: 12px;
        color: #909399;
        margin-left: 4px;
    }
}

// 修复图片预览层级问题
:deep(.el-image-viewer__wrapper) {
    z-index: 99999 !important;
}

:deep(.el-image__preview) {
    z-index: 99999 !important;
}

// 确保表格行不会覆盖预览
:deep(.el-table__row) {
    position: relative;
    z-index: 1;
}

// 确保表格操作列不会覆盖预览
:deep(.el-table__fixed-right) {
    z-index: 2;
}
</style>
