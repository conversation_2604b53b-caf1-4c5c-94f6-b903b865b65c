import request from '@/utils/request'

// 合伙人列表
export function apiPartnerLists(params: any) {
    return request.get({ url: '/partner.partner/lists', params })
}

// 合伙人详情
export function apiPartnerDetail(params: any) {
    return request.get({ url: '/partner.partner/detail', params })
}

// 添加/编辑合伙人
export function apiPartnerSave(params: any) {
    return request.post({ url: '/partner.partner/edit', params })
}

// 启用/禁用合伙人
export function apiPartnerStatus(params: any) {
    return request.post({ url: '/partner.partner/status', params })
}

// 获取已使用区域
export function apiPartnerGetArea(params?: any) {
    return request.get({ url: '/partner.partner/getArea', params })
}
