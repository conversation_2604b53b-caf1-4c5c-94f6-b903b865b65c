import request from '@/utils/request'

// 无人机列表
export function apiDroneLists(params: any) {
    return request.get({ url: '/marketing.drone/lists', params })
}

// 无人机详情
export function apiDroneDetail(params: any) {
    return request.get({ url: '/marketing.drone/detail', params })
}

// 添加/编辑无人机
export function apiDroneEdit(params: any) {
    return request.post({ url: '/marketing.drone/edit', params })
}

// 删除无人机
export function apiDroneDelete(params: any) {
    return request.get({ url: '/marketing.drone/del', params })
}

// 上/下架无人机
export function apiDroneStatus(params: any) {
    return request.post({ url: '/marketing.drone/status', params })
}
