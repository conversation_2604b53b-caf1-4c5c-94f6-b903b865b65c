<template>
    <el-card shadow="never" class="!border-none">
        <el-form :model="formData" inline>
            <el-form-item label="用户ID">
                <el-input class="ls-input" v-model="formData.user_id" placeholder="用户ID" />
            </el-form-item>
            <el-form-item label="关键词">
                <el-input class="ls-input" v-model="formData.partner_info" placeholder="昵称/手机号码" />
            </el-form-item>
            <el-form-item label="申请状态">
                <el-select class="ls-input" v-model="formData.apply_status" placeholder="请选择申请状态">
                    <el-option label="全部" value="" />
                    <el-option label="待审核" :value="0" />
                    <el-option label="已通过" :value="1" />
                    <el-option label="已驳回" :value="2" />
                </el-select>
            </el-form-item>
            <el-form-item label="申请时间">
                <data-picker
                    class="ls-input"
                    style="width: 280px"
                    v-model:start_time="formData.start_time"
                    v-model:end_time="formData.end_time"
                ></data-picker>
            </el-form-item>
            <el-form-item>
                <div class="flex">
                    <el-button type="primary" @click="getLists">查询</el-button>
                    <el-button @click="handleResetParams">重置</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-card>

    <el-card shadow="never" class="mt-4 !border-none">
        <div class="mt-3">
            <el-table :data="pager.lists" style="width: 100%" v-loading="pager.loading">
                <el-table-column property="user_id" label="用户ID" min-width="80" />
                <!-- <el-table-column label="头像" min-width="80">
                    <template #default="scope">
                        <div class="w-[60px] h-[60px]">
                            <el-image
                                style="width: 60px; height: 60px"
                                :src="scope.row.avatar"
                                :preview-src-list="[scope.row.avatar]"
                                :hide-on-click-modal="true"
                                :preview-teleported="true"
                                :fit="'cover'"
                            />
                        </div>
                    </template>
                </el-table-column> -->
                <el-table-column property="contact" label="昵称" min-width="120" />
                <el-table-column property="mobile" label="手机号码" min-width="120" />
                <el-table-column property="area_str" label="申请合伙人区域" min-width="200" />
                <el-table-column property="apply_status_str" label="申请状态" min-width="100" />
                <el-table-column property="create_time" label="申请时间" min-width="180" />
                <el-table-column label="操作" min-width="120" fixed="right">
                    <template #default="scope">
                        <div class="flex items-center">
                            <el-button
                                v-perms="['partner.partner_apply/detail']"
                                type="primary"
                                link
                                @click="handleDetail(scope.row.id)"
                            >
                                详情
                            </el-button>
                            <el-button
                                v-if="scope.row.apply_status == 0"
                                v-perms="['partner.partner_apply/audit']"
                                type="success"
                                link
                                @click="handleAudit(scope.row.id, 1)"
                            >
                                通过
                            </el-button>
                            <el-button
                                v-if="scope.row.apply_status == 0"
                                v-perms="['partner.partner_apply/audit']"
                                type="danger"
                                link
                                @click="handleReject(scope.row)"
                            >
                                驳回
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="flex justify-end mt-4">
            <pagination v-model="pager" @change="getLists" />
        </div>
    </el-card>

    <!-- 驳回弹窗 -->
    <el-dialog v-model="rejectDialogVisible" title="驳回申请" width="400px">
        <el-form :model="rejectForm" label-width="80px">
            <el-form-item label="驳回原因" required>
                <el-input
                    v-model="rejectForm.remarks"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入驳回原因"
                    maxlength="200"
                    show-word-limit
                />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="rejectDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmReject">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup name="partnerApply">
import { apiPartnerApplyLists, apiPartnerApplyAudit } from '@/api/master_worker/apply'
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { usePaging } from '@/hooks/usePaging'
import feedback from '@/utils/feedback'
import Pagination from '@/components/pagination/index.vue'
import DataPicker from '@/components/data-picker/index.vue'

const router = useRouter()

interface FormDataObj {
    user_id?: string
    partner_info?: string
    apply_status: number | string
    start_time: string
    end_time: string
}

const formData = ref<FormDataObj>({
    user_id: '',
    partner_info: '',
    apply_status: '',
    start_time: '',
    end_time: ''
})

const rejectDialogVisible = ref(false)
const rejectForm = ref({
    id: 0,
    remarks: ''
})

const { pager, getLists, resetPage, resetParams } = usePaging({
    fetchFun: apiPartnerApplyLists,
    params: formData.value
})

// 重置筛选框
const handleResetParams = () => {
    resetParams()
}

// 查看详情
const handleDetail = (id: number) => {
    router.push({
        path: '/master_worker/partner_apply/detail',
        query: { id }
    })
}

// 审核通过
const handleAudit = async (id: number, status: number) => {
    try {
        await feedback.confirm('确认通过该申请吗？')
        await apiPartnerApplyAudit({ id, apply_status: status })
        feedback.msgSuccess('操作成功')
        getLists()
    } catch (error) {
        // 用户取消操作
    }
}

// 驳回申请
const handleReject = (row: any) => {
    rejectForm.value.id = row.id
    rejectForm.value.remarks = ''
    rejectDialogVisible.value = true
}

// 确认驳回
const confirmReject = async () => {
    if (!rejectForm.value.remarks.trim()) {
        feedback.msgError('请输入驳回原因')
        return
    }
    
    try {
        await apiPartnerApplyAudit({
            id: rejectForm.value.id,
            apply_status: 2,
            remarks: rejectForm.value.remarks
        })
        feedback.msgSuccess('操作成功')
        rejectDialogVisible.value = false
        getLists()
    } catch (error) {
        feedback.msgError('操作失败')
    }
}

onMounted(() => {
    getLists()
})
</script>

<style lang="scss" scoped>
.ls-input {
    width: 280px;
}
</style>
