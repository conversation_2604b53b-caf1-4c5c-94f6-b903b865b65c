{
  "theme": "Atom One",
  "selectedAuthType": "oauth-personal",
  /* "mcpServers": {
    "context7": {
      "command": "npx",
      "args": [
        "-y",
        "@upstash/context7-mcp"
      ]
    }
  }, */
  "preferredEditor": "vscode",
  "fileFiltering": {
    "respectGitIgnore": true,
    "enableRecursiveFileSearch": false
  },
  "checkpointing": {
    "enabled": true
  },
  "contextFileName": "CLAUDE.md"
}