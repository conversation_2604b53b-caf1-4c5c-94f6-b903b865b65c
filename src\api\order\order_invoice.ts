import request from '@/utils/request'

// 订单开票列表
export function apiOrderInvoiceLists(params: any) {
    return request.get({ url: '/marketing.order_invoice/lists', params })
}

// 订单开票详情
export function apiOrderInvoiceDetail(params: any) {
    return request.get({ url: '/marketing.order_invoice/detail', params })
}

// 订单开票
export function apiOrderInvoiceInvoice(params: any) {
    return request.post({ url: '/marketing.order_invoice/invoice', params })
}
