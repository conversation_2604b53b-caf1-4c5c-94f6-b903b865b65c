import request from '@/utils/request'

// 租赁申请列表
export function apiLeaseLists(params: any) {
    return request.get({ url: '/marketing.lease/lists', params })
}

// 租赁申请详情
export function apiLeaseDetail(params: any) {
    return request.get({ url: '/marketing.lease/detail', params })
}

// 出租/关闭租赁申请
export function apiLeaseStatus(params: any) {
    return request.post({ url: '/marketing.lease/status', params })
}
