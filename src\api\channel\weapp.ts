import request from '@/utils/request'

// 设置用户微信小程序配置
export function setWeappConfig(params: any) {
    return request.post({ url: '/channel.mnp_settings/setConfig', params })
}

// 获取用户微信小程序配置
export function getWeappConfig() {
    return request.get({ url: '/channel.mnp_settings/getConfig' })
}

// 设置飞手微信小程序配置
export function setStaffWeappConfig(params: any) {
    return request.post({ url: '/channel.staff_mnp_settings/setConfig', params })
}

// 获取飞手微信小程序配置
export function getStaffWeappConfig() {
    return request.get({ url: '/channel.staff_mnp_settings/getConfig' })
}
